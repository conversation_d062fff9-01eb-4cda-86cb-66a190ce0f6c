{"version": 3, "file": "systemClipboard.js", "sourceRoot": "", "sources": ["../../../../src/ui/utils/systemClipboard.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AAEH,OAAO,UAAU,MAAM,YAAY,CAAC;AAEpC;;;GAGG;AACH,MAAM,OAAO,eAAe;IAClB,MAAM,CAAC,QAAQ,GAA2B,IAAI,CAAC;IAC/C,WAAW,GAAY,IAAI,CAAC;IAEpC;QACE,gDAAgD;QAChD,IAAI,CAAC,yBAAyB,EAAE,CAAC;IACnC,CAAC;IAED;;OAEG;IACI,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC;YAC9B,eAAe,CAAC,QAAQ,GAAG,IAAI,eAAe,EAAE,CAAC;QACnD,CAAC;QACD,OAAO,eAAe,CAAC,QAAQ,CAAC;IAClC,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,yBAAyB;QACrC,IAAI,CAAC;YACH,sDAAsD;YACtD,iEAAiE;YACjE,MAAM,UAAU,GAAG,MAAM,CAAC;YAC1B,MAAM,UAAU,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YACnC,MAAM,MAAM,GAAG,MAAM,UAAU,CAAC,IAAI,EAAE,CAAC;YACvC,IAAI,CAAC,WAAW,GAAG,MAAM,KAAK,UAAU,CAAC;QAC3C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,wEAAwE;YACxE,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;QAC3B,CAAC;IACH,CAAC;IAED;;;;OAIG;IACI,KAAK,CAAC,SAAS,CAAC,IAAY;QACjC,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,OAAO,KAAK,CAAC;QACf,CAAC;QAED,IAAI,CAAC;YACH,MAAM,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAC7B,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,2DAA2D;YAC3D,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;YACzB,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;;OAGG;IACI,KAAK,CAAC,QAAQ;QACnB,IAAI,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC;YACtB,OAAO,IAAI,CAAC;QACd,CAAC;QAED,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,MAAM,UAAU,CAAC,IAAI,EAAE,CAAC;YACrC,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,2DAA2D;YAC3D,IAAI,CAAC,WAAW,GAAG,KAAK,CAAC;YACzB,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;;OAGG;IACI,oBAAoB;QACzB,OAAO,IAAI,CAAC,WAAW,CAAC;IAC1B,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,qBAAqB;QAChC,MAAM,IAAI,CAAC,yBAAyB,EAAE,CAAC;IACzC,CAAC;;AAGH;;GAEG;AACH,MAAM,CAAC,MAAM,eAAe,GAAG,eAAe,CAAC,WAAW,EAAE,CAAC"}