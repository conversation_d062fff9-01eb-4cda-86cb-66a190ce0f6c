/**
 * @license
 * Copyright 2025 Google LLC
 * SPDX-License-Identifier: Apache-2.0
 */
import clipboardy from 'clipboardy';
/**
 * System clipboard utility that provides safe access to the system clipboard
 * with proper error handling and fallback behavior.
 */
export class SystemClipboard {
    static instance = null;
    isAvailable = true;
    constructor() {
        // Test clipboard availability on initialization
        this.testClipboardAvailability();
    }
    /**
     * Get the singleton instance of SystemClipboard
     */
    static getInstance() {
        if (!SystemClipboard.instance) {
            SystemClipboard.instance = new SystemClipboard();
        }
        return SystemClipboard.instance;
    }
    /**
     * Test if the system clipboard is available
     */
    async testClipboardAvailability() {
        try {
            // Try to write a test string first, then read it back
            // This handles the case where clipboard might be empty initially
            const testString = 'test';
            await clipboardy.write(testString);
            const result = await clipboardy.read();
            this.isAvailable = result === testString;
        }
        catch (error) {
            // Silently fail - clipboard might not be available in some environments
            this.isAvailable = false;
        }
    }
    /**
     * Write text to the system clipboard
     * @param text The text to write to clipboard
     * @returns Promise<boolean> True if successful, false otherwise
     */
    async writeText(text) {
        if (!this.isAvailable) {
            return false;
        }
        try {
            await clipboardy.write(text);
            return true;
        }
        catch (error) {
            // Silently fail - don't spam console with clipboard errors
            this.isAvailable = false;
            return false;
        }
    }
    /**
     * Read text from the system clipboard
     * @returns Promise<string | null> The clipboard text or null if unavailable/error
     */
    async readText() {
        if (!this.isAvailable) {
            return null;
        }
        try {
            const text = await clipboardy.read();
            return text;
        }
        catch (error) {
            // Silently fail - don't spam console with clipboard errors
            this.isAvailable = false;
            return null;
        }
    }
    /**
     * Check if the system clipboard is available
     * @returns boolean True if clipboard is available
     */
    isClipboardAvailable() {
        return this.isAvailable;
    }
    /**
     * Retry clipboard availability test
     */
    async retryAvailabilityTest() {
        await this.testClipboardAvailability();
    }
}
/**
 * Get the singleton system clipboard instance
 */
export const systemClipboard = SystemClipboard.getInstance();
//# sourceMappingURL=systemClipboard.js.map